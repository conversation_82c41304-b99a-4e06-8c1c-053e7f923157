import { shuffle } from 'lodash';
import { useGlobalInstructor } from '@composables';
import { useDialogStore, useMapStore } from '@stores';
import { Sequences, SystemMessage, UnifyInstructor } from '@types';

interface MessageState {
  currentMessageId: string;
  currentTimeout: NodeJS.Timeout | null;
  nextMessageTimeout: NodeJS.Timeout | null;
  sequenceMessageQueue: string[];
  triggeredSequenceMessages: Set<string>;
  triggeredConditionalMessages: Set<string>;
  triggeredRmiConditionalMessages: Set<string>;
  lastTriggeredSequenceMessageId: string;
  isProcessing: boolean;
  attemptsTriggered: number;
  rmiCooldowns: Map<string, number>;
}

type MessageType = 'timii' | 'sqkii' | 'nancii' | 'rmi';
type MessageCategory = 'sequence' | 'conditional' | 'rmi_conditional';

const MESSAGE_TYPES = {
  TIMII: 'timii',
  SQKII: 'sqkii',
  NANCII: 'nancii',
  RMI: 'rmi',
} as const;

const MESSAGE_CATEGORIES = {
  SEQUENCE: 'sequence',
  CONDITIONAL: 'conditional',
  RMI_CONDITIONAL: 'rmi_conditional',
} as const;

const TIMING_CONSTANTS = {
  MESSAGE_CHECK_DELAY: 300, // 300ms between checks
  AUTO_CLOSE_TIMEOUT: 15000, // 15 seconds auto-close
  UI_SETTLE_DELAY: 50, // 50ms for UI to settle
  RMI_COOLDOWN_PERIOD: 60 * 1000, // 1 minute in milliseconds
  MAX_SHUFFLE_ATTEMPTS: 15,
  MAX_UI_WAIT_ATTEMPTS: 10,
} as const;

// const SHRINK_MESSAGE_KEYS = {
//   smaller_public: 'SHRINKINGCIRCLE_SHRANKED_AHEAD',
//   smaller_private: 'SHRINKINGCIRCLE_SHRANKED_AHEAD_1',
//   default: 'SHRINKINGCIRCLE_SHRANKED_AHEAD_2',
// } as const;

// const RMI_OUTLET_TYPES = ['poi', 'landmark', 'merchant_rmi'] as RmiOutletType[];

const createMessage = (
  id: string,
  text: string,
  type: MessageType,
  messageType: MessageCategory,
  options: Partial<SystemMessage> = {},
): SystemMessage => ({
  id,
  text,
  type,
  messageType,
  ...options,
});

export function useSystemMessage() {
  const storeDialog = useDialogStore();
  const storeMap = useMapStore();
  // const storeUser = useUserStore();

  const { isLoading } = storeToRefs(storeMap);
  const { showInstructor } = storeToRefs(storeDialog);
  const { openUnifyInstructor, closeUnifyInstructor } = useGlobalInstructor();
  const { activePage, activeDialog, openDialog } = useMicroRoute();
  const { t } = useI18n();

  const state = reactive<MessageState>({
    currentMessageId: '',
    currentTimeout: null,
    nextMessageTimeout: null,
    sequenceMessageQueue: [],
    triggeredSequenceMessages: new Set(),
    triggeredConditionalMessages: new Set(),
    triggeredRmiConditionalMessages: new Set(),
    lastTriggeredSequenceMessageId: '',
    isProcessing: false,
    attemptsTriggered: 0,
    rmiCooldowns: new Map(),
  });

  const activePowerUp = computed(() =>
    [
      storeDialog.showCoinSonarGUI,
      storeDialog.showBeaconGUI,
      storeDialog.showMetalDetectorGUI,
      storeDialog.showInventoryQuickAccess,
      storeDialog.showSilverCoinSelectCircle,
    ].some(Boolean),
  );

  const isIdleInHomeScreen = computed(() => {
    if (isLoading.value || activePowerUp.value) return false;
    const isHomeScreen = activePage.value === 'home';
    const noDialog = !activeDialog.value;
    return [isHomeScreen, noDialog].every(Boolean);
  });

  // ===== SYSTEM MESSAGES =====
  const systemSequenceMessages = computed<SystemMessage[]>(() => [
    createMessage(
      'dialogue_tips_random_1',
      t('DIALOGUE_TIPS_RANDOM_1'),
      MESSAGE_TYPES.SQKII,
      MESSAGE_CATEGORIES.SEQUENCE,
      {
        onClick: () => {
          openDialog('tips_trick');
        },
      },
    ),
    createMessage(
      'dialogue_tips_random_2',
      t('DIALOGUE_TIPS_RANDOM_2'),
      MESSAGE_TYPES.TIMII,
      MESSAGE_CATEGORIES.SEQUENCE,
    ),
    // createMessage(
    //   'dialogue_tips_random_3',
    //   t('DIALOGUE_TIPS_RANDOM_3'),
    //   MESSAGE_TYPES.TIMII,
    //   MESSAGE_CATEGORIES.SEQUENCE,
    // ),
    // createMessage(
    //   'dialogue_healthsocial_tips_random_1',
    //   t('DIALOGUE_HEALTHSOCIAL_TIPS_RANDOM_1'),
    //   MESSAGE_TYPES.TIMII,
    //   MESSAGE_CATEGORIES.SEQUENCE,
    // ),
    // createMessage(
    //   'dialogue_tips_random_5',
    //   t('DIALOGUE_TIPS_RANDOM_5'),
    //   MESSAGE_TYPES.TIMII,
    //   MESSAGE_CATEGORIES.SEQUENCE,
    // ),
    // createMessage(
    //   'dialogue_healthsocial_tips_random_2',
    //   t('DIALOGUE_HEALTHSOCIAL_TIPS_RANDOM_2'),
    //   MESSAGE_TYPES.TIMII,
    //   MESSAGE_CATEGORIES.SEQUENCE,
    //   {
    //     persistent: true,
    //   },
    // ),
  ]);

  // ===== EXTERNAL MESSAGES =====
  // const silverShrinkText = computed(() => {
  //   if (!shrinkData.value?.bottomMessage) return '';

  //   const { bottomMessage } = shrinkData.value;
  //   const { type, this_use, smallest } = bottomMessage;

  //   const translationKey =
  //     SHRINK_MESSAGE_KEYS[type as keyof typeof SHRINK_MESSAGE_KEYS] || SHRINK_MESSAGE_KEYS.default;

  //   return t(translationKey, {
  //     USED: this_use,
  //     SMALLEST: smallest,
  //   });
  // });

  // const systemExternalMessages = computed<SystemMessage[]>(() => [
  //   createMessage(
  //     'silver_coin_shrink',
  //     silverShrinkText.value,
  //     MESSAGE_TYPES.NANCII,
  //     MESSAGE_CATEGORIES.CONDITIONAL,
  //     {
  //       backdropCss: true,
  //       agent: shrinkSovContext.value?.agent,
  //       tag: shrinkSovContext.value?.tag,
  //       condition: () => false,
  //     },
  //   ),
  // ]);

  // ===== RMI CONDITIONAL MESSAGES =====
  // const createRmiConditionalMessage = (outlet: RmiOutlet): SystemMessage => {
  //   const messageId = outlet.unique_id;

  //   const canTrigger = (): boolean => {
  //     const now = Date.now();

  //     const lastTriggered = state.rmiCooldowns.get(outlet.unique_id);
  //     if (lastTriggered && now - lastTriggered < TIMING_CONSTANTS.RMI_COOLDOWN_PERIOD) return false; // Still in cooldown
  //     return true;
  //   };

  //   const text = outlet.fun_facts[Math.floor(Math.random() * outlet.fun_facts.length)];
  //   const images = outlet.type === 'merchant_rmi' ? outlet.images : [];

  //   return createMessage(
  //     messageId,
  //     t(text),
  //     MESSAGE_TYPES.RMI,
  //     MESSAGE_CATEGORIES.RMI_CONDITIONAL,
  //     {
  //       images,
  //       rmiOutlet: outlet,
  //       condition: canTrigger,
  //       onTrigger: () => {
  //         state.rmiCooldowns.set(outlet.unique_id, Date.now());
  //       },
  //     },
  //   );
  // };

  // const filteredRMIOutlets = computed(() =>
  //   rmiOutlets.value.filter((outlet) => RMI_OUTLET_TYPES.includes(outlet.type)),
  // );

  // const systemRmiConditionalMessages = computed<SystemMessage[]>(() =>
  //   filteredRMIOutlets.value.map(createRmiConditionalMessage),
  // );

  // ===== CONDITIONAL MESSAGES =====
  // const isUserLogged = computed(() => !!user.value?.mobile_number);

  // const isWithinCircle = computed(() => {
  //   const pt = point(lastLocations.value);
  //   return groupedSilverCoins.value.ongoing.some((coin) => booleanPointInPolygon(pt, coin));
  // });

  // const isWithinSmallestCircle = computed(() => {
  //   const pt = point(lastLocations.value);
  //   return groupedSilverCoins.value.ongoing.some(
  //     (coin) => !coin.properties.canUsePowerUp && booleanPointInPolygon(pt, coin),
  //   );
  // });

  // const canTriggerSurvey = computed(() => {
  //   return isTriggerSurvey.value || canTriggerEndGameSurvey.value;
  // });

  // const mapDistanceSilverCoins = computed(() => {
  //   const ongoingCoins = groupedSilverCoins.value.ongoing;

  //   const userPoint = lastLocations.value;

  //   return ongoingCoins.map((coin) => {
  //     const { center, radius } = coin.properties.circle;
  //     const distanceToCenter = turfDistance(userPoint, [center.lng, center.lat], {
  //       units: 'meters',
  //     });

  //     const distanceToEdge = Math.max(0, distanceToCenter - radius);

  //     return {
  //       ...coin,
  //       properties: {
  //         ...coin.properties,
  //         distance: Number(distanceToEdge.toFixed(2)),
  //       },
  //     };
  //   });
  // });

  // const isNearSilverCoin = computed(() => {
  //   const data = mapDistanceSilverCoins.value.filter(
  //     (c) => c.properties.distance < 150, // 150m
  //   );
  //   return data.length === 1;
  // });

  // const isNearMultipleSilverCoin = computed(() => {
  //   const data = mapDistanceSilverCoins.value.filter(
  //     (c) => c.properties.distance < 150, // 150m
  //   );
  //   return data.length > 1;
  // });

  // const allDBSCoinsFound = computed(() => {
  //   return totalHeartLandCoin.value <= 0 && totalSqkiiCoin.value > 0 && allCoinFound.value;
  // });

  // const randomEnabledGPSMessage = computed(() => {
  //   const messages = ['DIALOGUE_GPS_ENABLEGPS', 'GPS_DIALOGUE_1', 'GPS_DIALOGUE_2'];
  //   return randomMessage(messages);
  // });

  // const randomNearbySilverCoinMessage = computed(() => {
  //   const messages = ['DIALOGUE_TIPS_ENTERSSILVERCIRCLE', 'DIALOGUE_TIPS_ENTERSSILVERCIRCLE_2'];
  //   return randomMessage(messages);
  // });

  // const systemConditionalMessages = computed<SystemMessage[]>(() => [
  //   createMessage(
  //     'dbs_coin_to_be_found',
  //     t('DBS_COIN_TO_BE_FOUND', {
  //       DBS_REMAINING: totalHeartLandCoin.value,
  //     }),
  //     MESSAGE_TYPES.SQKII,
  //     MESSAGE_CATEGORIES.CONDITIONAL,
  //     {
  //       condition: () => totalHeartLandCoin.value > 0,
  //     },
  //   ),
  //   createMessage(
  //     'dialog_all_dbs_coin_found',
  //     t('DIALOG_ALL_DBS_COIN_FOUND', {
  //       SILVER_REMAINING: totalSqkiiCoin.value,
  //     }),
  //     MESSAGE_TYPES.SQKII,
  //     MESSAGE_CATEGORIES.CONDITIONAL,
  //     {
  //       condition: () => allDBSCoinsFound.value,
  //     },
  //   ),
  //   createMessage(
  //     'dialogue_tips_random_4',
  //     t('DIALOGUE_TIPS_RANDOM_4'),
  //     MESSAGE_TYPES.TIMII,
  //     MESSAGE_CATEGORIES.SEQUENCE,
  //     {
  //       condition: () => isUserLogged.value,
  //     },
  //   ),
  //   createMessage(
  //     'dialogue_signup_reminder_1',
  //     t('DIALOGUE_SIGNUP_REMINDER_1'),
  //     MESSAGE_TYPES.TIMII,
  //     MESSAGE_CATEGORIES.SEQUENCE,
  //     {
  //       condition: () => !isUserLogged.value,
  //     },
  //   ),
  //   createMessage(
  //     randomEnabledGPSMessage.value.toLocaleLowerCase(),
  //     t(randomEnabledGPSMessage.value),
  //     MESSAGE_TYPES.TIMII,
  //     MESSAGE_CATEGORIES.CONDITIONAL,
  //     {
  //       condition: () => !isEnabledGPS.value,
  //     },
  //   ),
  //   createMessage(
  //     randomNearbySilverCoinMessage.value.toLocaleLowerCase(),
  //     t(randomNearbySilverCoinMessage.value),
  //     MESSAGE_TYPES.TIMII,
  //     MESSAGE_CATEGORIES.CONDITIONAL,
  //     {
  //       condition: () => isWithinCircle.value,
  //     },
  //   ),

  //   createMessage(
  //     'dialogue_survey',
  //     t('DIALOGUE_SURVEY'),
  //     MESSAGE_TYPES.SQKII,
  //     MESSAGE_CATEGORIES.CONDITIONAL,
  //     {
  //       condition: () => canTriggerSurvey.value,
  //       onClick: () => {
  //         openDialog(canTriggerEndGameSurvey.value ? 'survey' : 'mid_survey');
  //       },
  //     },
  //   ),
  //   createMessage(
  //     'dialogue_tips_newcoindropped',
  //     t('DIALOGUE_TIPS_NEWCOINDROPPED'),
  //     MESSAGE_TYPES.TIMII,
  //     MESSAGE_CATEGORIES.CONDITIONAL,
  //     {
  //       condition: () => newCoinDrop.value.length > 0,
  //       onClick: () => {
  //         openDialog('new_coin_drop', {
  //           coins: newCoinDrop.value,
  //         });
  //       },
  //     },
  //   ),
  //   createMessage(
  //     'dialogue_gps_enterssmallestcircle',
  //     t('DIALOGUE_GPS_ENTERSSMALLESTCIRCLE'),
  //     MESSAGE_TYPES.TIMII,
  //     MESSAGE_CATEGORIES.CONDITIONAL,
  //     {
  //       condition: () => isWithinSmallestCircle.value,
  //     },
  //   ),
  //   createMessage(
  //     'dialogue_tips_nearsilvercircle',
  //     t('DIALOGUE_TIPS_NEARSILVERCIRCLE'),
  //     MESSAGE_TYPES.TIMII,
  //     MESSAGE_CATEGORIES.CONDITIONAL,
  //     {
  //       condition: () => isNearSilverCoin.value,
  //     },
  //   ),
  //   createMessage(
  //     'dialogue_tips_multiplesilvercircles',
  //     t('DIALOGUE_TIPS_MULTIPLESILVERCIRCLES'),
  //     MESSAGE_TYPES.TIMII,
  //     MESSAGE_CATEGORIES.CONDITIONAL,
  //     {
  //       condition: () => isNearMultipleSilverCoin.value,
  //     },
  //   ),
  // ]);

  // ===== MESSAGE COLLECTIONS =====
  const systemMessages = computed<SystemMessage[]>(() => [
    ...systemSequenceMessages.value,
    // ...systemExternalMessages.value,
    // ...systemConditionalMessages.value,
    // ...systemRmiConditionalMessages.value,
  ]);

  const sequenceMessages = computed<SystemMessage[]>(() =>
    systemMessages.value.filter((msg) => msg.messageType === MESSAGE_CATEGORIES.SEQUENCE),
  );

  const conditionalMessages = computed<SystemMessage[]>(() =>
    systemMessages.value.filter((msg) => msg.messageType === MESSAGE_CATEGORIES.CONDITIONAL),
  );

  const rmiConditionalMessages = computed<SystemMessage[]>(() =>
    systemMessages.value.filter((msg) => msg.messageType === MESSAGE_CATEGORIES.RMI_CONDITIONAL),
  );

  // ===== SEQUENCE QUEUE MANAGEMENT =====
  const sequenceQueueManager = {
    initialize(avoidMessageId?: string): void {
      const messageIds = sequenceMessages.value.map((msg) => msg.id);

      if (messageIds.length <= 1) {
        state.sequenceMessageQueue = messageIds;
        state.triggeredSequenceMessages.clear();
        return;
      }

      // Shuffle until we get a different first message than the one to avoid
      let shuffledIds: string[];
      let attempts = 0;

      do {
        shuffledIds = shuffle(messageIds);
        attempts++;
      } while (
        avoidMessageId &&
        shuffledIds[0] === avoidMessageId &&
        attempts < TIMING_CONSTANTS.MAX_SHUFFLE_ATTEMPTS &&
        messageIds.length > 1
      );

      state.sequenceMessageQueue = shuffledIds;
      state.triggeredSequenceMessages.clear();
    },

    resetIfNeeded(): void {
      if (state.triggeredSequenceMessages.size >= sequenceMessages.value.length) {
        const lastMessageId = state.lastTriggeredSequenceMessageId;
        state.triggeredSequenceMessages.clear();
        this.initialize(lastMessageId);
      }
    },

    getNext(): SystemMessage | null {
      this.resetIfNeeded();

      if (state.sequenceMessageQueue.length === 0) {
        return null;
      }

      // Find the next untriggered message in the queue
      for (const messageId of state.sequenceMessageQueue) {
        const message = systemMessages.value.find((msg) => msg.id === messageId);
        if (message && !state.triggeredSequenceMessages.has(messageId)) {
          return message;
        }
      }

      return null;
    },
  };

  // ===== MESSAGE CHECKERS =====
  const messageCheckers = {
    checkRmiConditional(): SystemMessage | null {
      for (const message of rmiConditionalMessages.value) {
        if (message.condition && message.condition()) {
          if (!state.triggeredRmiConditionalMessages.has(message.id)) {
            return message;
          }
        }
      }
      return null;
    },

    checkConditional(): SystemMessage | null {
      for (const message of conditionalMessages.value) {
        if (message.condition && message.condition()) {
          if (!state.triggeredConditionalMessages.has(message.id)) {
            return message;
          }
        }
      }
      return null;
    },

    allSequenceTriggered(): boolean {
      return state.triggeredSequenceMessages.size >= sequenceMessages.value.length;
    },

    allRmiConditionalTriggered(): boolean {
      const availableRmiMessages = rmiConditionalMessages.value.filter(
        (msg) => msg.condition && msg.condition(),
      );
      return availableRmiMessages.length === 0;
    },

    allConditionalTriggered(): boolean {
      const availableConditionalMessages = conditionalMessages.value.filter(
        (msg) => msg.condition && msg.condition(),
      );
      return (
        availableConditionalMessages.length === 0 ||
        availableConditionalMessages.every((msg) => state.triggeredConditionalMessages.has(msg.id))
      );
    },
  };

  // ===== RMI COOLDOWN MANAGER =====
  const rmiCooldownManager = {
    handlePlayerLeaving(): void {
      // When player leaves, clear any active RMI cooldowns that have expired
      // This allows re-triggering when they return after the cooldown period
      const now = Date.now();
      const expiredCooldowns: string[] = [];

      state.rmiCooldowns.forEach((timestamp, outletId) => {
        if (now - timestamp >= TIMING_CONSTANTS.RMI_COOLDOWN_PERIOD) {
          expiredCooldowns.push(outletId);
        }
      });

      // Remove expired cooldowns
      expiredCooldowns.forEach((outletId) => {
        state.rmiCooldowns.delete(outletId);
      });
    },
  };

  // ===== COMPLETION CHECKER =====
  const completionChecker = {
    allTypesCompleted(): boolean {
      const allRmiTriggered = messageCheckers.allRmiConditionalTriggered();
      const allConditionalTriggered = messageCheckers.allConditionalTriggered();
      const allSequenceTriggered = messageCheckers.allSequenceTriggered();

      // Only consider complete when we have messages to trigger
      const hasRmiMessages = rmiConditionalMessages.value.some(
        (msg) => msg.condition && msg.condition(),
      );
      const hasConditionalMessages = conditionalMessages.value.some(
        (msg) => msg.condition && msg.condition(),
      );
      const hasSequenceMessages = sequenceMessages.value.length > 0;

      // If no messages exist for a type, consider it "completed"
      const rmiComplete = !hasRmiMessages || allRmiTriggered;
      const conditionalComplete = !hasConditionalMessages || allConditionalTriggered;
      const sequenceComplete = !hasSequenceMessages || allSequenceTriggered;

      const isComplete = rmiComplete && conditionalComplete && sequenceComplete;

      return isComplete;
    },

    resetAllStates(): void {
      state.attemptsTriggered = 0;
      state.triggeredConditionalMessages.clear();
      state.triggeredRmiConditionalMessages.clear();
      state.triggeredSequenceMessages.clear();
      const lastMessageId = state.lastTriggeredSequenceMessageId;
      sequenceQueueManager.initialize(lastMessageId);
    },
  };

  // Trigger a message
  function triggerMessage(message: SystemMessage, isFromSystem = true): void {
    if (state.isProcessing) return;

    state.isProcessing = true;
    state.currentMessageId = message.id;

    uiUtils.clearAllTimeouts();
    // Call onTrigger if exists
    void message.onTrigger?.();

    const sequences = [
      {
        backdropCss: message.backdropCss ?? false,
        message: message.text,
        images: message.images || [],
        rmiOutlet: message.rmiOutlet,
        actions: {
          cb(_, close) {
            close();
            void message.onClick?.();
            handleMessageComplete(message);
          },
          closeX(_, close) {
            close();
            handleMessageComplete(message);
          },
        },
      },
    ] as Sequences[];

    // Open instructor with message
    openUnifyInstructor(message.type, {
      agent: message.agent,
      tag: message.tag,
      hiddenAnims: message.hiddenAnims ?? false,
      sequences,
    } as UnifyInstructor);

    // Auto-close logic:
    // 1. If message has persistent = true, never auto-close
    // 2. If message is from external source (not system), never auto-close
    // 3. Only auto-close messages from system that are not persistent
    const shouldAutoClose = isFromSystem && !message.persistent;

    if (shouldAutoClose) {
      state.currentTimeout = setTimeout(() => {
        closeUnifyInstructor();
        handleMessageComplete(message);
      }, TIMING_CONSTANTS.AUTO_CLOSE_TIMEOUT);
    }
  }

  // Handle message completion
  function handleMessageComplete(message: SystemMessage): void {
    // Prevent double handling
    if (!state.isProcessing) return;

    switch (message.messageType) {
      case MESSAGE_CATEGORIES.SEQUENCE:
        state.triggeredSequenceMessages.add(message.id);
        state.lastTriggeredSequenceMessageId = message.id;
        break;
      case MESSAGE_CATEGORIES.CONDITIONAL:
        state.triggeredConditionalMessages.add(message.id);
        break;
      case MESSAGE_CATEGORIES.RMI_CONDITIONAL:
        break;
      default:
        break;
    }

    state.isProcessing = false;
    state.currentMessageId = '';
    uiUtils.clearAllTimeouts();

    // Wait for UI to fully update before scheduling next check
    uiUtils.waitForUpdate(() => {
      messageScheduler.scheduleNext();
    });
  }

  // ===== UI UTILITIES =====
  const uiUtils = {
    waitForUpdate(callback: () => void, maxAttempts = TIMING_CONSTANTS.MAX_UI_WAIT_ATTEMPTS): void {
      let attempts = 0;

      const checkUIState = () => {
        attempts++;

        if ((!showInstructor.value && isIdleInHomeScreen.value) || attempts >= maxAttempts) {
          callback();
          return;
        }

        // Wait a bit more and check again
        setTimeout(checkUIState, TIMING_CONSTANTS.UI_SETTLE_DELAY);
      };

      // Start checking after a small initial delay
      setTimeout(checkUIState, TIMING_CONSTANTS.UI_SETTLE_DELAY);
    },

    clearAllTimeouts(): void {
      [state.currentTimeout, state.nextMessageTimeout].forEach((timeout) => {
        if (timeout) {
          clearTimeout(timeout);
          timeout = null;
        }
      });
    },

    findMessageById(messageId: string): SystemMessage | undefined {
      return systemMessages.value.find((msg) => msg.id === messageId);
    },
  };

  // ===== MESSAGE SCHEDULER =====
  const messageScheduler = {
    scheduleNext(): void {
      if (state.nextMessageTimeout) clearTimeout(state.nextMessageTimeout);

      state.nextMessageTimeout = setTimeout(() => {
        this.checkAndTrigger();
      }, TIMING_CONSTANTS.MESSAGE_CHECK_DELAY);
    },

    checkAndTrigger(): void {
      // Extra check: ensure showInstructor is actually false
      if (showInstructor.value) {
        setTimeout(() => {
          this.checkAndTrigger();
        }, TIMING_CONSTANTS.UI_SETTLE_DELAY);
        return;
      }

      if (!isIdleInHomeScreen.value || state.isProcessing) {
        this.scheduleNext();
        return;
      }

      // Priority 1: Check RMI conditional messages first (only if not all triggered)
      if (!messageCheckers.allRmiConditionalTriggered()) {
        const rmiConditionalMessage = messageCheckers.checkRmiConditional();
        if (rmiConditionalMessage) {
          triggerMessage(rmiConditionalMessage);
          return;
        }
      }

      // Priority 2: Check conditional messages (only if all RMI completed and not all conditional triggered)
      if (
        messageCheckers.allRmiConditionalTriggered() &&
        !messageCheckers.allConditionalTriggered()
      ) {
        const conditionalMessage = messageCheckers.checkConditional();
        if (conditionalMessage) {
          triggerMessage(conditionalMessage);
          return;
        }
      }

      // Priority 3: Check sequence messages (only if all RMI and conditional completed)
      if (
        messageCheckers.allRmiConditionalTriggered() &&
        messageCheckers.allConditionalTriggered() &&
        !messageCheckers.allSequenceTriggered()
      ) {
        const sequenceMessage = sequenceQueueManager.getNext();
        if (sequenceMessage) {
          triggerMessage(sequenceMessage);
          return;
        }
      }

      // Check if all message types have been completed
      if (completionChecker.allTypesCompleted()) {
        completionChecker.resetAllStates();
        // Immediately schedule next check after reset
        this.scheduleNext();
        return;
      }

      // If no messages to trigger, schedule next check
      this.scheduleNext();
    },
  };

  function forceTriggermessage(messageId: string): void {
    const message = uiUtils.findMessageById(messageId);
    if (message) {
      triggerMessage(message);
    }
  }

  function stopTriggerMessage(): void {
    closeUnifyInstructor();
    uiUtils.clearAllTimeouts();
    state.isProcessing = false;
    state.currentMessageId = '';
  }

  function handleNextMessage(): void {
    const currentMessage = uiUtils.findMessageById(state.currentMessageId);
    if (currentMessage) {
      handleMessageComplete(currentMessage);
    }
  }

  function handleUIAction(actionId: string): void {
    const actionMap: Record<string, () => void> = {
      goTipTrick: () => openDialog('tips_trick'),
      goSignUp: () => openDialog('signup'),
    };

    const action = actionMap[actionId];
    if (action) action();
  }

  function handleClickAction(event: Event): void {
    const target = event.target as HTMLElement;
    if (target.id) handleUIAction(target.id);
  }

  watch(
    isIdleInHomeScreen,
    (isIdle) => {
      if (isIdle && !state.isProcessing && !showInstructor.value) {
        uiUtils.waitForUpdate(() => {
          messageScheduler.checkAndTrigger();
        });
      } else if (!isIdle) {
        // Handle player leaving - clean up expired RMI cooldowns
        rmiCooldownManager.handlePlayerLeaving();

        if (state.isProcessing && showInstructor.value) {
          // Only auto-close if a message is currently being shown
          closeUnifyInstructor();
          const currentMessage = uiUtils.findMessageById(state.currentMessageId);
          if (currentMessage) {
            handleMessageComplete(currentMessage);
          }
        }
      }
    },
    { immediate: true },
  );

  onMounted(async () => {
    await nextTick();
    sequenceQueueManager.initialize();
    addEventListener('click', handleClickAction);
  });

  onUnmounted(() => {
    uiUtils.clearAllTimeouts();
    completionChecker.resetAllStates();
    removeEventListener('click', handleClickAction);
  });

  return {
    forceTriggermessage,
    stopTriggerMessage,
    closeUnifyInstructor,
    openUnifyInstructor,
    handleNextMessage,
  };
}
